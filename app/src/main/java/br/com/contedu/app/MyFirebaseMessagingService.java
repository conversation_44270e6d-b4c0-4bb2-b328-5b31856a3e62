package br.com.contedu.app;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.os.Build;
import androidx.core.app.NotificationCompat;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

public class MyFirebaseMessagingService extends FirebaseMessagingService {

    private static final String TAG = "MyFirebaseMsgService";

    public static String notificationId;

    @Override
    public void onNewToken(String s) {
        super.onNewToken(s);
        Log.e("newToken", s);
        getSharedPreferences("_", MODE_PRIVATE).edit().putString("fb", s).apply();
    }

    public static String getToken(Context context) {
        return context.getSharedPreferences("_", MODE_PRIVATE).getString("fb", "empty");
    }

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {

        String title  = remoteMessage.getData().get("title");
        String text   = remoteMessage.getData().get("text");
        String link = remoteMessage.getData().get("link");
        String action = remoteMessage.getData().get("action");

        String newNotificationId = remoteMessage.getData().get("notificationId");

        Log.d(TAG, "title: " + title + " text " + text + " link " + link);

        Log.d(TAG, "old: " + notificationId + " new " + newNotificationId);


        if (action.equals("cancel")   ) {

            if (newNotificationId.equals(notificationId)) {
                NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
                manager.cancel(0);
                Log.d(TAG, "Cancel: " + notificationId);
            }

        } else {

            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.putExtra("link",link);

            PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);

            String channelId = "channelId";

            NotificationCompat.Builder builder = new NotificationCompat.Builder(this, channelId)
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .setContentTitle(title)
                    .setContentText(text)
                    .setAutoCancel(true)
                    //.setVibrate(new long[] { 1000, 1000 })
                    .setContentIntent(pendingIntent);

            NotificationManager notificationManager =
                    (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

            // Since android Oreo notification channel is needed.
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                NotificationChannel channel = new NotificationChannel(channelId,
                        "Channel Contedu",
                        NotificationManager.IMPORTANCE_DEFAULT);
                notificationManager.createNotificationChannel(channel);
                Log.d(TAG, "Channel Conedu "  );
            }

            notificationManager.notify(0 /* ID of notification */, builder.build());

            //NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            //manager.notify(0, builder.build());

            notificationId = newNotificationId;
            Log.d(TAG, "Notify " + notificationId );

        }

    }
}
