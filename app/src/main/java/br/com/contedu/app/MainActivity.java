package br.com.contedu.app;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.webkit.*;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.messaging.FirebaseMessaging;

import java.io.File;
import java.io.IOException;

public class MainActivity extends AppCompatActivity {

    //public static final String HOST = "http://********";
    public static final String HOST = "https://app.contedu.com.br";
    private WebView webView;

    private static final String TAG = MainActivity.class.getSimpleName();

    //Open image
    private static final int FILECHOOSER_RESULTCODE = 1;
    public static final int REQUEST_SELECT_FILE = 100;
    private ValueCallback<Uri> mUploadMessage;
    private Uri mCapturedImageURI = null;

    // the same for Android 5.0 methods only
    private ValueCallback<Uri[]> mFilePathCallback;
    private String mCameraPhotoPath;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        getSupportActionBar().hide();

        webView = (WebView)findViewById(R.id.webView);
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webView.setWebViewClient(new MyBrowser());
        webView.setWebChromeClient(new MyWebChromeClient());
        webView.addJavascriptInterface(new WebAppInterface(this), "Android");
        webView.loadUrl(getUrl());
    }

    private String getUrl() {

        String url = HOST;
        String cfToken  =  getLink();

        if (cfToken != null && !cfToken.equals("")) {
            url =  url + getLink();
        } else {
            if (HOST.contains("********")) {
                url =  url + "/dashboard";
            }
        }
        Log.d(TAG, url);
        return url;
    }

    private String getLink() {

        String link = "";
        // Intent from Notification
        if (getIntent().getExtras() != null) {
            for (String key : getIntent().getExtras().keySet()) {
                Object value = getIntent().getExtras().get(key);
                //Log.d(TAG, "Key: " + key + " Value: " + value);
                if (key.equals("link")) {
                    link = (String) value;
                    //getIntent().putExtra("link","");
                }
            }
        }

        return link;
    }

    private class MyBrowser extends WebViewClient {
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {

            return false;

            /*
            if (url.contains("contedu.com.br")) {
                return false;  // This is my web site, so do not override; let my WebView load the page
            }
            // Otherwise, the link is not for a page on my site, so launch another Activity that handles URLs
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            startActivity(intent);
            return true;

             */

        }


    }

     // return here when file selected
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode != FILECHOOSER_RESULTCODE || mFilePathCallback == null) {
            super.onActivityResult(requestCode, resultCode, data);
            return;
        }

        Uri[] results = null;
        // check that the response is a good one
        if (resultCode == Activity.RESULT_OK) {
             String dataString = data.getDataString();
             if (dataString != null) {
                 results = new Uri[]{Uri.parse(dataString)};
             }
        }
        mFilePathCallback.onReceiveValue(results);
        mFilePathCallback = null;
    }

    @Override
    public void onBackPressed() {
        if(webView.canGoBack()){
            webView.goBack();
        }
        else {
            super.onBackPressed();
        }
    }

    private class MyWebChromeClient extends WebChromeClient {

        public boolean onShowFileChooser(
                WebView webView, ValueCallback<Uri[]> filePathCallback,
                FileChooserParams fileChooserParams) {
            if (mFilePathCallback != null) {
                mFilePathCallback.onReceiveValue(null);
            }
            mFilePathCallback = filePathCallback;

            Intent contentSelectionIntent = new Intent(Intent.ACTION_GET_CONTENT);
            contentSelectionIntent.addCategory(Intent.CATEGORY_OPENABLE);
            //contentSelectionIntent.setType("image/*");
            contentSelectionIntent.setType("*/*");
            startActivityForResult(Intent.createChooser(contentSelectionIntent, getString(R.string.image_chooser)), FILECHOOSER_RESULTCODE);

            return true;
        }
    }

}